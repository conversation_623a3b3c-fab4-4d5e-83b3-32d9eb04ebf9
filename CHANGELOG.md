# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [0.1.0] - 2025-01-01

### Added
- Initial release of the Vue.js and React Dependency Analyzer
- Multi-framework support for Vue.js, React, and TypeScript
- Comprehensive dependency analysis with circular dependency detection
- Multiple output formats: JSON, DOT (Graphviz), Mermaid, and Text Tree
- Extensible parser architecture with BaseParser, VueParser, and ReactParser
- Complete test suite with unit, integration, and parser-specific tests
- Comprehensive documentation including API reference, developer guide, and testing guide
- CLI interface with multiple options and output formats
- Example projects for testing and demonstration

### Project Structure
- Reorganized codebase into proper Python package structure under `src/diff_analyzer/`
- Moved example projects to `examples/` directory
- Comprehensive test suite in `tests/` directory
- Documentation in `docs/` directory
- Proper package configuration with `pyproject.toml`

### Documentation
- Complete API documentation with examples
- Developer guide for extending the system
- Testing guide with coverage requirements
- Updated README with installation and usage instructions
- All code examples verified to work with current implementation

### Technical Features
- Tree-sitter-typescript for accurate parsing
- NetworkX for graph analysis and algorithms
- Click for CLI interface
- Comprehensive error handling and logging
- Type hints throughout the codebase
- High test coverage (80%+ overall, 90%+ core modules)

### Development Tools
- uv support for modern Python package management
- pytest configuration with coverage reporting
- Code quality tools: black, isort, flake8, mypy
- Proper .gitignore for Python projects
- CI/CD ready configuration

## [Unreleased]

### Planned Features
- Support for more frameworks (Angular, Svelte)
- Integration with popular bundlers (Webpack, Vite)
- Performance optimizations for large projects
- Web-based visualization interface
- CI/CD integration tools

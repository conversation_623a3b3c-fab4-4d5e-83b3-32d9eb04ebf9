<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_6b508a39.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">80%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button current">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.1">coverage.py v7.10.1</a>,
            created at 2025-08-01 21:25 +0800
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">function<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb___init___py.html">src/diff_analyzer/__init__.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_analyzer_py.html#t139">src/diff_analyzer/dependency_analyzer.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_analyzer_py.html#t139"><data value='init__'>BaseParser.__init__</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_analyzer_py.html#t153">src/diff_analyzer/dependency_analyzer.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_analyzer_py.html#t153"><data value='parse_file'>BaseParser.parse_file</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>1</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_analyzer_py.html#t171">src/diff_analyzer/dependency_analyzer.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_analyzer_py.html#t171"><data value='read_file_content'>BaseParser._read_file_content</data></a></td>
                <td>9</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="8 9">89%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_analyzer_py.html#t201">src/diff_analyzer/dependency_analyzer.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_analyzer_py.html#t201"><data value='get_parser_for_file'>BaseParser._get_parser_for_file</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_analyzer_py.html#t226">src/diff_analyzer/dependency_analyzer.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_analyzer_py.html#t226"><data value='resolve_import_path'>BaseParser._resolve_import_path</data></a></td>
                <td>23</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="20 23">87%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_analyzer_py.html#t305">src/diff_analyzer/dependency_analyzer.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_analyzer_py.html#t305"><data value='find_project_root'>BaseParser._find_project_root</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_analyzer_py.html#t317">src/diff_analyzer/dependency_analyzer.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_analyzer_py.html#t317"><data value='extract_imports_from_tree'>BaseParser._extract_imports_from_tree</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_analyzer_py.html#t326">src/diff_analyzer/dependency_analyzer.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_analyzer_py.html#t326"><data value='walk_tree_for_imports'>BaseParser._walk_tree_for_imports</data></a></td>
                <td>13</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="12 13">92%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_analyzer_py.html#t345">src/diff_analyzer/dependency_analyzer.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_analyzer_py.html#t345"><data value='process_import_statement'>BaseParser._process_import_statement</data></a></td>
                <td>16</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="15 16">94%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_analyzer_py.html#t379">src/diff_analyzer/dependency_analyzer.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_analyzer_py.html#t379"><data value='process_require_statement'>BaseParser._process_require_statement</data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_analyzer_py.html#t399">src/diff_analyzer/dependency_analyzer.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_analyzer_py.html#t399"><data value='process_dynamic_import'>BaseParser._process_dynamic_import</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_analyzer_py.html#t419">src/diff_analyzer/dependency_analyzer.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_analyzer_py.html#t419"><data value='extract_import_names'>BaseParser._extract_import_names</data></a></td>
                <td>22</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="22 22">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_analyzer_py.html">src/diff_analyzer/dependency_analyzer.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_analyzer_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>49</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="49 49">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_graph_py.html#t55">src/diff_analyzer/dependency_graph.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_graph_py.html#t55"><data value='init__'>DependencyGraph.__init__</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_graph_py.html#t77">src/diff_analyzer/dependency_graph.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_graph_py.html#t77"><data value='analyze_project'>DependencyGraph.analyze_project</data></a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_graph_py.html#t116">src/diff_analyzer/dependency_graph.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_graph_py.html#t116"><data value='find_files_to_analyze'>DependencyGraph._find_files_to_analyze</data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_graph_py.html#t135">src/diff_analyzer/dependency_graph.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_graph_py.html#t135"><data value='parse_file'>DependencyGraph._parse_file</data></a></td>
                <td>11</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="8 11">73%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_graph_py.html#t152">src/diff_analyzer/dependency_graph.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_graph_py.html#t152"><data value='node_to_attributes'>DependencyGraph._node_to_attributes</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_graph_py.html#t164">src/diff_analyzer/dependency_graph.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_graph_py.html#t164"><data value='build_graph_edges'>DependencyGraph._build_graph_edges</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_graph_py.html#t179">src/diff_analyzer/dependency_graph.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_graph_py.html#t179"><data value='resolve_local_import'>DependencyGraph._resolve_local_import</data></a></td>
                <td>21</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="14 21">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_graph_py.html#t219">src/diff_analyzer/dependency_graph.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_graph_py.html#t219"><data value='analyze_graph_properties'>DependencyGraph._analyze_graph_properties</data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_graph_py.html#t246">src/diff_analyzer/dependency_graph.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_graph_py.html#t246"><data value='find_circular_dependencies'>DependencyGraph.find_circular_dependencies</data></a></td>
                <td>6</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="4 6">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_graph_py.html#t263">src/diff_analyzer/dependency_graph.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_graph_py.html#t263"><data value='get_entry_points'>DependencyGraph.get_entry_points</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_graph_py.html#t270">src/diff_analyzer/dependency_graph.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_graph_py.html#t270"><data value='get_leaf_nodes'>DependencyGraph.get_leaf_nodes</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_graph_py.html#t277">src/diff_analyzer/dependency_graph.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_graph_py.html#t277"><data value='get_dependency_chain'>DependencyGraph.get_dependency_chain</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_graph_py.html#t286">src/diff_analyzer/dependency_graph.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_graph_py.html#t286"><data value='dfs'>DependencyGraph.get_dependency_chain.dfs</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_graph_py.html#t299">src/diff_analyzer/dependency_graph.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_graph_py.html#t299"><data value='get_dependents'>DependencyGraph.get_dependents</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_graph_py.html#t306">src/diff_analyzer/dependency_graph.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_graph_py.html#t306"><data value='get_dependencies'>DependencyGraph.get_dependencies</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_graph_py.html#t313">src/diff_analyzer/dependency_graph.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_graph_py.html#t313"><data value='get_external_dependencies'>DependencyGraph.get_external_dependencies</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_graph_py.html#t325">src/diff_analyzer/dependency_graph.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_graph_py.html#t325"><data value='get_statistics'>DependencyGraph.get_statistics</data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_graph_py.html#t355">src/diff_analyzer/dependency_graph.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_graph_py.html#t355"><data value='get_file_type_distribution'>DependencyGraph._get_file_type_distribution</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_graph_py.html#t365">src/diff_analyzer/dependency_graph.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_graph_py.html#t365"><data value='get_most_imported_files'>DependencyGraph._get_most_imported_files</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_graph_py.html#t375">src/diff_analyzer/dependency_graph.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_graph_py.html#t375"><data value='get_most_dependent_files'>DependencyGraph._get_most_dependent_files</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_graph_py.html#t385">src/diff_analyzer/dependency_graph.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_graph_py.html#t385"><data value='get_shortest_path'>DependencyGraph.get_shortest_path</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_graph_py.html#t392">src/diff_analyzer/dependency_graph.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_graph_py.html#t392"><data value='is_reachable'>DependencyGraph.is_reachable</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_graph_py.html">src/diff_analyzer/dependency_graph.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_graph_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>30</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="30 30">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_main_py.html#t59">src/diff_analyzer/main.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_main_py.html#t59"><data value='main'>main</data></a></td>
                <td>35</td>
                <td>35</td>
                <td>0</td>
                <td class="right" data-ratio="0 35">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_main_py.html#t123">src/diff_analyzer/main.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_main_py.html#t123"><data value='generate_output'>_generate_output</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_main_py.html#t148">src/diff_analyzer/main.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_main_py.html#t148"><data value='generate_stats_only'>_generate_stats_only</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_main_py.html#t196">src/diff_analyzer/main.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_main_py.html#t196"><data value='cli'>cli</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_main_py.html#t205">src/diff_analyzer/main.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_main_py.html#t205"><data value='path'>path</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_main_py.html#t231">src/diff_analyzer/main.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_main_py.html#t231"><data value='analyze_file'>analyze_file</data></a></td>
                <td>27</td>
                <td>27</td>
                <td>0</td>
                <td class="right" data-ratio="0 27">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_main_py.html">src/diff_analyzer/main.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_main_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>31</td>
                <td>0</td>
                <td>5</td>
                <td class="right" data-ratio="31 31">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_output_formatters_py.html#t48">src/diff_analyzer/output_formatters.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_output_formatters_py.html#t48"><data value='init__'>OutputFormatter.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_output_formatters_py.html#t58">src/diff_analyzer/output_formatters.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_output_formatters_py.html#t58"><data value='get_relative_path'>OutputFormatter._get_relative_path</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_output_formatters_py.html#t81">src/diff_analyzer/output_formatters.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_output_formatters_py.html#t81"><data value='get_file_label'>OutputFormatter._get_file_label</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_output_formatters_py.html#t118">src/diff_analyzer/output_formatters.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_output_formatters_py.html#t118"><data value='format'>JSONFormatter.format</data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_output_formatters_py.html#t174">src/diff_analyzer/output_formatters.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_output_formatters_py.html#t174"><data value='format_import_info'>JSONFormatter._format_import_info</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_output_formatters_py.html#t191">src/diff_analyzer/output_formatters.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_output_formatters_py.html#t191"><data value='format'>DOTFormatter.format</data></a></td>
                <td>43</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="36 43">84%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_output_formatters_py.html#t265">src/diff_analyzer/output_formatters.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_output_formatters_py.html#t265"><data value='format'>MermaidFormatter.format</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_output_formatters_py.html#t274">src/diff_analyzer/output_formatters.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_output_formatters_py.html#t274"><data value='format_flowchart'>MermaidFormatter._format_flowchart</data></a></td>
                <td>27</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="27 27">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_output_formatters_py.html#t325">src/diff_analyzer/output_formatters.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_output_formatters_py.html#t325"><data value='format_graph'>MermaidFormatter._format_graph</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_output_formatters_py.html#t342">src/diff_analyzer/output_formatters.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_output_formatters_py.html#t342"><data value='format'>TextTreeFormatter.format</data></a></td>
                <td>35</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="31 35">89%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_output_formatters_py.html#t401">src/diff_analyzer/output_formatters.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_output_formatters_py.html#t401"><data value='format_dependency_tree'>TextTreeFormatter._format_dependency_tree</data></a></td>
                <td>23</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="18 23">78%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_output_formatters_py.html">src/diff_analyzer/output_formatters.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_output_formatters_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>23</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="22 23">96%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_react_parser_py.html#t45">src/diff_analyzer/react_parser.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_react_parser_py.html#t45"><data value='init__'>ReactParser.__init__</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_react_parser_py.html#t66">src/diff_analyzer/react_parser.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_react_parser_py.html#t66"><data value='parse_file'>ReactParser.parse_file</data></a></td>
                <td>16</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="13 16">81%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_react_parser_py.html#t123">src/diff_analyzer/react_parser.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_react_parser_py.html#t123"><data value='extract_react_specific_imports'>ReactParser._extract_react_specific_imports</data></a></td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_react_parser_py.html#t166">src/diff_analyzer/react_parser.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_react_parser_py.html#t166"><data value='extract_jsx_component_usage'>ReactParser._extract_jsx_component_usage</data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_react_parser_py.html#t182">src/diff_analyzer/react_parser.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_react_parser_py.html#t182"><data value='walk_tree_for_jsx'>ReactParser._walk_tree_for_jsx</data></a></td>
                <td>15</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="15 15">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_react_parser_py.html#t205">src/diff_analyzer/react_parser.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_react_parser_py.html#t205"><data value='find_component_import'>ReactParser._find_component_import</data></a></td>
                <td>13</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="13 13">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_react_parser_py.html#t243">src/diff_analyzer/react_parser.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_react_parser_py.html#t243"><data value='extract_exports_from_tree'>ReactParser._extract_exports_from_tree</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_react_parser_py.html#t252">src/diff_analyzer/react_parser.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_react_parser_py.html#t252"><data value='walk_tree_for_exports'>ReactParser._walk_tree_for_exports</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_react_parser_py.html#t262">src/diff_analyzer/react_parser.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_react_parser_py.html#t262"><data value='extract_react_components'>ReactParser._extract_react_components</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_react_parser_py.html#t272">src/diff_analyzer/react_parser.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_react_parser_py.html#t272"><data value='get_line_number'>ReactParser._get_line_number</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_react_parser_py.html#t276">src/diff_analyzer/react_parser.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_react_parser_py.html#t276"><data value='is_entry_point'>ReactParser._is_entry_point</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_react_parser_py.html#t298">src/diff_analyzer/react_parser.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_react_parser_py.html#t298"><data value='get_react_specific_info'>ReactParser.get_react_specific_info</data></a></td>
                <td>7</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="4 7">57%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_react_parser_py.html#t319">src/diff_analyzer/react_parser.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_react_parser_py.html#t319"><data value='uses_react_hooks'>ReactParser._uses_react_hooks</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_react_parser_py.html#t326">src/diff_analyzer/react_parser.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_react_parser_py.html#t326"><data value='get_hooks_used'>ReactParser._get_hooks_used</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_react_parser_py.html#t334">src/diff_analyzer/react_parser.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_react_parser_py.html#t334"><data value='get_component_type'>ReactParser._get_component_type</data></a></td>
                <td>10</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="8 10">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_react_parser_py.html#t349">src/diff_analyzer/react_parser.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_react_parser_py.html#t349"><data value='has_jsx'>ReactParser._has_jsx</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_react_parser_py.html#t360">src/diff_analyzer/react_parser.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_react_parser_py.html#t360"><data value='extract_prop_types'>ReactParser._extract_prop_types</data></a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_react_parser_py.html#t381">src/diff_analyzer/react_parser.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_react_parser_py.html#t381"><data value='has_state_usage'>ReactParser._has_state_usage</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_react_parser_py.html">src/diff_analyzer/react_parser.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_react_parser_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>23</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="23 23">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_vue_parser_py.html#t42">src/diff_analyzer/vue_parser.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_vue_parser_py.html#t42"><data value='init__'>VueParser.__init__</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_vue_parser_py.html#t55">src/diff_analyzer/vue_parser.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_vue_parser_py.html#t55"><data value='parse_file'>VueParser.parse_file</data></a></td>
                <td>20</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="17 20">85%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_vue_parser_py.html#t118">src/diff_analyzer/vue_parser.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_vue_parser_py.html#t118"><data value='extract_script_sections'>VueParser._extract_script_sections</data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_vue_parser_py.html#t139">src/diff_analyzer/vue_parser.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_vue_parser_py.html#t139"><data value='get_parser_for_script'>VueParser._get_parser_for_script</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_vue_parser_py.html#t149">src/diff_analyzer/vue_parser.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_vue_parser_py.html#t149"><data value='extract_vue_component_imports'>VueParser._extract_vue_component_imports</data></a></td>
                <td>22</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="16 22">73%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_vue_parser_py.html#t211">src/diff_analyzer/vue_parser.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_vue_parser_py.html#t211"><data value='extract_template_component_usage'>VueParser._extract_template_component_usage</data></a></td>
                <td>15</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="14 15">93%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_vue_parser_py.html#t244">src/diff_analyzer/vue_parser.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_vue_parser_py.html#t244"><data value='extract_exports_from_tree'>VueParser._extract_exports_from_tree</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_vue_parser_py.html#t253">src/diff_analyzer/vue_parser.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_vue_parser_py.html#t253"><data value='walk_tree_for_exports'>VueParser._walk_tree_for_exports</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_vue_parser_py.html#t263">src/diff_analyzer/vue_parser.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_vue_parser_py.html#t263"><data value='get_line_number'>VueParser._get_line_number</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_vue_parser_py.html#t267">src/diff_analyzer/vue_parser.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_vue_parser_py.html#t267"><data value='is_entry_point'>VueParser._is_entry_point</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_vue_parser_py.html#t285">src/diff_analyzer/vue_parser.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_vue_parser_py.html#t285"><data value='get_vue_specific_info'>VueParser.get_vue_specific_info</data></a></td>
                <td>8</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="5 8">62%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_vue_parser_py.html#t307">src/diff_analyzer/vue_parser.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_vue_parser_py.html#t307"><data value='extract_component_name'>VueParser._extract_component_name</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_vue_parser_py.html#t314">src/diff_analyzer/vue_parser.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_vue_parser_py.html#t314"><data value='extract_props'>VueParser._extract_props</data></a></td>
                <td>12</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="10 12">83%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_vue_parser_py.html#t334">src/diff_analyzer/vue_parser.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_vue_parser_py.html#t334"><data value='extract_computed_properties'>VueParser._extract_computed_properties</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_vue_parser_py.html#t342">src/diff_analyzer/vue_parser.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_vue_parser_py.html#t342"><data value='extract_methods'>VueParser._extract_methods</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_vue_parser_py.html">src/diff_analyzer/vue_parser.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_vue_parser_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>20</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="20 20">100%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>941</td>
                <td>190</td>
                <td>6</td>
                <td class="right" data-ratio="751 941">80%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.1">coverage.py v7.10.1</a>,
            created at 2025-08-01 21:25 +0800
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>

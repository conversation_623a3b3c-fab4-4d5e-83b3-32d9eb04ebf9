# Testing Guide

## Overview

This guide covers how to run tests, write new tests, and understand the testing strategy for the Vue.js and React Dependency Analyzer.

## Test Structure

The test suite is organized into several categories:

```
tests/
├── __init__.py                 # Test package initialization
├── conftest.py                 # Shared fixtures and configuration
├── test_dependency_analyzer.py # Core analyzer tests
├── test_vue_parser.py          # Vue parser tests
├── test_react_parser.py        # React parser tests
├── test_dependency_graph.py    # Graph analysis tests
├── test_output_formatters.py   # Output formatter tests
└── test_integration.py         # End-to-end integration tests
```

## Running Tests

### Prerequisites

Install test dependencies:

```bash
# Using uv (recommended)
uv install --group test

# Or using pip
pip install -e ".[test]"
```

### Basic Test Execution

```bash
# Using uv (recommended)
uv run pytest

# Run with coverage
uv run pytest --cov=src --cov-report=html

# Run specific test file
uv run pytest tests/test_vue_parser.py

# Run specific test method
uv run pytest tests/test_vue_parser.py::TestVueParser::test_parse_file_success

# Run tests with specific markers
uv run pytest -m unit          # Unit tests only
uv run pytest -m integration   # Integration tests only
uv run pytest -m "not slow"    # Exclude slow tests

# Using pip installation
pytest
pytest --cov=src --cov-report=html
```

### Test Configuration

The test configuration is defined in `pyproject.toml`:

```toml
[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--verbose",
    "--cov=src",
    "--cov-report=term-missing",
    "--cov-report=html:htmlcov",
    "--cov-report=xml",
    "--cov-fail-under=80",
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "parser: marks tests related to file parsing",
    "formatter: marks tests related to output formatting",
    "graph: marks tests related to dependency graph operations",
]
```

## Test Categories

### Unit Tests

Test individual components in isolation:

```python
@pytest.mark.unit
class TestImportInfo:
    def test_import_info_creation(self):
        """Test creating an ImportInfo instance."""
        import_info = ImportInfo(
            source_file="/test/src/main.ts",
            imported_module="vue",
            import_type=ImportType.NAMED,
            dependency_type=DependencyType.EXTERNAL,
            imported_names=["createApp"],
            line_number=1
        )
        
        assert import_info.source_file == "/test/src/main.ts"
        assert import_info.imported_module == "vue"
```

### Integration Tests

Test complete workflows:

```python
@pytest.mark.integration
class TestVueProjectIntegration:
    def test_vue_project_analysis(self):
        """Test complete Vue project analysis."""
        graph = DependencyGraph(str(self.project_dir))
        graph.analyze_project()
        
        # Verify results
        assert len(graph.nodes) > 0
        stats = graph.get_statistics()
        assert stats["total_files"] > 0
```

### Parser Tests

Test file parsing capabilities:

```python
@pytest.mark.parser
class TestVueParser:
    def test_extract_script_sections(self):
        """Test extracting script sections from Vue SFC."""
        content = '''
        <template><div>Hello</div></template>
        <script lang="ts">
        import { defineComponent } from 'vue'
        export default defineComponent({})
        </script>
        '''
        
        sections = self.parser._extract_script_sections(content)
        assert len(sections) == 1
        assert sections[0][1] == "ts"
```

## Writing Tests

### Test Fixtures

Use shared fixtures from `conftest.py`:

```python
def test_with_temp_project(temp_project_dir):
    """Test using temporary project directory."""
    # temp_project_dir is automatically created and cleaned up
    test_file = temp_project_dir / "test.vue"
    test_file.write_text("<template></template>")
    
    # Your test logic here
    assert test_file.exists()

def test_with_sample_data(sample_vue_file_content, sample_dependency_node):
    """Test using sample data fixtures."""
    # Use pre-defined sample content and nodes
    assert "template" in sample_vue_file_content
    assert sample_dependency_node.file_path.endswith(".vue")
```

### Mocking External Dependencies

Use mocking for external dependencies:

```python
from unittest.mock import Mock, patch, mock_open

@patch('builtins.open', new_callable=mock_open, read_data="test content")
def test_read_file_content(mock_file):
    """Test file reading with mocked file system."""
    parser = BaseParser()
    content = parser._read_file_content("/test/file.ts")
    
    assert content == "test content"
    mock_file.assert_called_once_with("/test/file.ts", 'r', encoding='utf-8')

@patch.object(VueParser, '_read_file_content')
def test_parse_file_with_mock(mock_read):
    """Test parsing with mocked file content."""
    mock_read.return_value = '''
    <script>
    export default { name: 'Test' }
    </script>
    '''
    
    parser = VueParser()
    result = parser.parse_file("/test/Component.vue")
    
    assert result is not None
    assert result.exports == ["Test"]
```

### Testing Error Conditions

Test error handling and edge cases:

```python
def test_parse_invalid_file():
    """Test handling of invalid file content."""
    parser = VueParser()
    
    # Test with non-existent file
    result = parser.parse_file("/nonexistent/file.vue")
    assert result is None
    
    # Test with malformed content
    with patch.object(parser, '_read_file_content', return_value="invalid content"):
        result = parser.parse_file("/test/invalid.vue")
        # Should handle gracefully without crashing
        assert result is None or isinstance(result, DependencyNode)

def test_circular_dependency_detection():
    """Test detection of circular dependencies."""
    graph = DependencyGraph("/test")
    
    # Create circular dependency: A -> B -> C -> A
    graph.graph.add_edge("/test/A.vue", "/test/B.vue")
    graph.graph.add_edge("/test/B.vue", "/test/C.vue")
    graph.graph.add_edge("/test/C.vue", "/test/A.vue")
    
    cycles = graph.find_circular_dependencies()
    assert len(cycles) > 0
```

### Performance Testing

Test performance with larger datasets:

```python
@pytest.mark.slow
def test_large_project_performance():
    """Test performance with large project."""
    import time
    
    # Create large mock project
    graph = DependencyGraph("/large/project")
    
    start_time = time.time()
    graph.analyze_project()
    end_time = time.time()
    
    # Should complete within reasonable time
    assert end_time - start_time < 30  # 30 seconds max
    
    # Should handle large number of files
    assert len(graph.nodes) > 100
```

## Test Data Management

### Creating Test Projects

Use temporary directories for test projects:

```python
import tempfile
import shutil
from pathlib import Path

class TestProjectSetup:
    def setup_method(self):
        """Create temporary test project."""
        self.temp_dir = Path(tempfile.mkdtemp())
        self.project_dir = self.temp_dir / "test_project"
        self.project_dir.mkdir()
        
        # Create project structure
        src_dir = self.project_dir / "src"
        src_dir.mkdir()
        
        # Create test files
        (src_dir / "main.ts").write_text('''
        import App from './App.vue'
        import { createApp } from 'vue'
        createApp(App).mount('#app')
        ''')
        
        (src_dir / "App.vue").write_text('''
        <template><div>App</div></template>
        <script>
        export default { name: 'App' }
        </script>
        ''')
    
    def teardown_method(self):
        """Clean up test project."""
        shutil.rmtree(self.temp_dir, ignore_errors=True)
```

### Sample Data Fixtures

Create reusable sample data:

```python
# In conftest.py
@pytest.fixture
def complex_vue_component():
    """Complex Vue component for testing."""
    return '''
    <template>
      <div class="user-profile">
        <UserCard :user="user" @click="handleClick" />
        <ContactInfo :contact="user.contact" />
        <ActionButton @action="handleAction">Save</ActionButton>
      </div>
    </template>

    <script lang="ts">
    import { defineComponent, ref, computed } from 'vue'
    import UserCard from './UserCard.vue'
    import ContactInfo from '../common/ContactInfo.vue'
    import ActionButton from '@/components/ActionButton.vue'
    import { User } from '@/types/user'
    import { apiService } from '@/services/api'

    export default defineComponent({
      name: 'UserProfile',
      components: { UserCard, ContactInfo, ActionButton },
      setup() {
        const user = ref<User | null>(null)
        return { user }
      }
    })
    </script>
    '''
```

## Continuous Integration

### GitHub Actions Configuration

```yaml
# .github/workflows/test.yml
name: Tests

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [3.11, 3.12]
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v3
      with:
        python-version: ${{ matrix.python-version }}
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -e ".[test]"
    
    - name: Run tests
      run: |
        pytest --cov=. --cov-report=xml
    
    - name: Upload coverage
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
```

### Pre-commit Hooks

```yaml
# .pre-commit-config.yaml
repos:
  - repo: https://github.com/psf/black
    rev: 23.7.0
    hooks:
      - id: black
  
  - repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort
  
  - repo: https://github.com/pycqa/flake8
    rev: 6.0.0
    hooks:
      - id: flake8
  
  - repo: local
    hooks:
      - id: pytest
        name: pytest
        entry: pytest
        language: system
        pass_filenames: false
        always_run: true
```

## Coverage Requirements

Maintain high test coverage:

```bash
# Using uv
uv run pytest --cov=src --cov-report=html --cov-report=term-missing

# Check coverage threshold (automatically enforced in pyproject.toml)
uv run pytest --cov=src --cov-fail-under=80

# Using pip installation
pytest --cov=src --cov-report=html --cov-report=term-missing
```

Target coverage levels:
- **Overall**: 80%+
- **Core modules**: 90%+
- **Parsers**: 85%+
- **Formatters**: 80%+

## Debugging Tests

### Running Tests in Debug Mode

```bash
# Using uv
uv run pytest -v                    # Run with verbose output
uv run pytest -s                    # Run with debug output
uv run pytest --pdb                 # Drop into debugger on failure

# Run single test with debugging
uv run pytest -s -v tests/test_vue_parser.py::TestVueParser::test_specific_method

# Using pip installation
pytest -v
pytest -s
pytest --pdb
```

### Using IDE Debugging

Configure your IDE to run pytest with debugging support:

```python
# Add breakpoints in your test code
def test_debug_example():
    parser = VueParser()
    content = "..."
    
    import pdb; pdb.set_trace()  # Debugger breakpoint
    
    result = parser.parse_file("/test/file.vue")
    assert result is not None
```

## Best Practices

1. **Test one thing at a time** - Each test should verify a single behavior
2. **Use descriptive test names** - Names should explain what is being tested
3. **Arrange, Act, Assert** - Structure tests clearly
4. **Use fixtures for setup** - Avoid repetitive setup code
5. **Mock external dependencies** - Keep tests isolated and fast
6. **Test edge cases** - Include error conditions and boundary cases
7. **Keep tests fast** - Use `@pytest.mark.slow` for longer tests
8. **Maintain test data** - Keep sample files and fixtures up to date

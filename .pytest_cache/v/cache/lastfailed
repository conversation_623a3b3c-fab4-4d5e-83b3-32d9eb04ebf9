{"tests/test_dependency_analyzer.py::TestBaseParser::test_resolve_import_path_absolute": true, "tests/test_dependency_analyzer.py::TestBaseParser::test_is_entry_point": true, "tests/test_dependency_analyzer.py::TestBaseParser::test_clean_import_path": true, "tests/test_dependency_analyzer.py::TestBaseParser::test_extract_exports_from_tree": true, "tests/test_dependency_analyzer.py::TestBaseParser::test_node_to_attributes": true, "tests/test_dependency_analyzer.py::TestBaseParserIntegration::test_parse_typescript_file": true, "tests/test_dependency_analyzer.py::TestBaseParserIntegration::test_parse_jsx_file": true, "tests/test_dependency_graph.py::TestDependencyGraph::test_find_files_to_analyze_empty_directory": true, "tests/test_dependency_graph.py::TestDependencyGraph::test_node_to_attributes": true, "tests/test_dependency_graph.py::TestDependencyGraph::test_build_graph_edges": true, "tests/test_dependency_graph.py::TestDependencyGraph::test_find_circular_dependencies_self_loop": true, "tests/test_dependency_graph.py::TestDependencyGraph::test_find_dependency_path_exists": true, "tests/test_dependency_graph.py::TestDependencyGraph::test_find_dependency_path_not_exists": true, "tests/test_dependency_graph.py::TestDependencyGraph::test_find_dependency_path_same_node": true, "tests/test_dependency_graph.py::TestDependencyGraph::test_analyze_project_integration": true, "tests/test_output_formatters.py::TestDOTFormatter::test_format_basic": true, "tests/test_output_formatters.py::TestDOTFormatter::test_format_with_external_dependencies": true, "tests/test_output_formatters.py::TestDOTFormatter::test_format_with_clustering": true, "tests/test_output_formatters.py::TestTextTreeFormatter::test_format_basic": true, "tests/test_output_formatters.py::TestTextTreeFormatter::test_format_with_details": true, "tests/test_output_formatters.py::TestTextTreeFormatter::test_format_with_circular_dependencies": true, "tests/test_react_parser.py::TestReactParser::test_parse_file_success": true, "tests/test_react_parser.py::TestReactParser::test_parse_file_error_handling": true, "tests/test_react_parser.py::TestReactParser::test_get_component_type": true, "tests/test_react_parser.py::TestReactParser::test_extract_prop_types": true, "tests/test_react_parser.py::TestReactParser::test_get_react_specific_info": true, "tests/test_react_parser.py::TestReactParserIntegration::test_parse_complete_react_component": true, "tests/test_react_parser.py::TestReactParserIntegration::test_parse_react_hooks_only_file": true, "tests/test_vue_parser.py::TestVueParser::test_parse_file_success": true, "tests/test_vue_parser.py::TestVueParser::test_parse_file_error_handling": true, "tests/test_vue_parser.py::TestVueParser::test_extract_script_sections_setup": true, "tests/test_vue_parser.py::TestVueParser::test_extract_props": true, "tests/test_vue_parser.py::TestVueParser::test_extract_computed_properties": true, "tests/test_vue_parser.py::TestVueParser::test_extract_methods": true}
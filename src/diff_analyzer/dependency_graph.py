"""
Dependency Graph Analyzer

This module provides comprehensive dependency graph analysis for Vue.js and React projects.
It builds directed graphs of file dependencies, detects circular dependencies, analyzes
project structure, and provides various metrics and insights about the codebase.

Key features:
- Multi-framework support (Vue.js, React, TypeScript)
- Circular dependency detection
- Entry point and leaf node identification
- External dependency tracking
- Project statistics and metrics
- Path analysis between files
"""

import os
from pathlib import Path
from typing import Dict, List,Optional, Tuple
from collections import defaultdict
import networkx as nx

from .dependency_analyzer import DependencyNode, DependencyType
from .vue_parser import VueParser
from .react_parser import ReactParser


class DependencyGraph:
    """
    Manages and analyzes dependency relationships in a project.

    This class builds a directed graph representation of file dependencies,
    providing methods for analysis, circular dependency detection, and
    various project metrics.

    Attributes:
        project_root (Path): Absolute path to the project root directory
        nodes (Dict[str, DependencyNode]): Mapping of file paths to dependency nodes
        graph (nx.DiGraph): NetworkX directed graph of dependencies
        vue_parser (VueParser): Parser for Vue.js files
        react_parser (ReactParser): Parser for React/TypeScript files
        supported_extensions (Set[str]): File extensions that can be analyzed

    Examples:
        >>> graph = DependencyGraph("/path/to/project")
        >>> graph.analyze_project()
        >>> stats = graph.get_statistics()
        >>> print(f"Total files: {stats['total_files']}")
        >>>
        >>> cycles = graph.find_circular_dependencies()
        >>> if cycles:
        ...     print(f"Found {len(cycles)} circular dependencies")
    """

    def __init__(self, project_root: str):
        """
        Initialize the dependency graph analyzer.

        Args:
            project_root (str): Path to the project root directory

        Examples:
            >>> graph = DependencyGraph("/path/to/vue-project")
            >>> graph = DependencyGraph("/path/to/react-project")
        """
        self.project_root = Path(project_root).resolve()
        self.nodes: Dict[str, DependencyNode] = {}
        self.graph = nx.DiGraph()

        # Initialize parsers
        self.vue_parser = VueParser()
        self.react_parser = ReactParser()

        # File extensions to process
        self.supported_extensions = {'.vue', '.jsx', '.tsx', '.ts', '.js'}

    def analyze_project(self, exclude_patterns: Optional[List[str]] = None) -> None:
        """
        Analyze the entire project and build the dependency graph.

        Scans the project directory for supported files, parses each file
        to extract dependencies, and builds a directed graph of relationships.

        Args:
            exclude_patterns (Optional[List[str]]): Patterns to exclude from analysis.
                Defaults to common build/dependency directories.

        Examples:
            >>> graph = DependencyGraph("/path/to/project")
            >>> graph.analyze_project()  # Use default exclusions
            >>>
            >>> # Custom exclusions
            >>> graph.analyze_project(exclude_patterns=['node_modules', 'dist', 'coverage'])
        """
        if exclude_patterns is None:
            exclude_patterns = ['node_modules', '.git', 'dist', 'build', '.next', '.nuxt']
        
        # Find all relevant files
        files_to_analyze = self._find_files_to_analyze(exclude_patterns)
        
        print(f"Found {len(files_to_analyze)} files to analyze")
        
        # Parse each file
        for file_path in files_to_analyze:
            node = self._parse_file(file_path)
            if node:
                self.nodes[file_path] = node
                self.graph.add_node(file_path, **self._node_to_attributes(node))
        
        # Build edges based on imports
        self._build_graph_edges()
        
        # Analyze graph properties
        self._analyze_graph_properties()
    
    def _find_files_to_analyze(self, exclude_patterns: List[str]) -> List[str]:
        """Find all files that should be analyzed."""
        files = []
        
        for root, dirs, filenames in os.walk(self.project_root):
            # Skip excluded directories
            dirs[:] = [d for d in dirs if not any(pattern in d for pattern in exclude_patterns)]
            
            for filename in filenames:
                file_path = Path(root) / filename
                
                # Check if file has supported extension
                if file_path.suffix in self.supported_extensions:
                    # Skip if file path contains excluded patterns
                    if not any(pattern in str(file_path) for pattern in exclude_patterns):
                        files.append(str(file_path))
        
        return files
    
    def _parse_file(self, file_path: str) -> Optional[DependencyNode]:
        """Parse a single file using the appropriate parser."""
        try:
            ext = Path(file_path).suffix.lower()
            
            if ext == '.vue':
                return self.vue_parser.parse_file(file_path)
            elif ext in ['.jsx', '.tsx', '.ts', '.js']:
                return self.react_parser.parse_file(file_path)
            else:
                print(f"Unsupported file type: {file_path}")
                return None
                
        except Exception as e:
            print(f"Error parsing {file_path}: {e}")
            return None
    
    def _node_to_attributes(self, node: DependencyNode) -> Dict:
        """Convert a DependencyNode to graph node attributes."""
        return {
            'imports_count': len(node.imports),
            'exports_count': len(node.exports),
            'is_entry_point': node.is_entry_point,
            'is_leaf_node': node.is_leaf_node,
            'file_type': Path(node.file_path).suffix,
            'local_imports': len([imp for imp in node.imports if imp.dependency_type == DependencyType.LOCAL]),
            'external_imports': len([imp for imp in node.imports if imp.dependency_type == DependencyType.EXTERNAL])
        }
    
    def _build_graph_edges(self) -> None:
        """Build edges in the graph based on import relationships."""
        for source_file, node in self.nodes.items():
            for import_info in node.imports:
                # Only create edges for local dependencies
                if import_info.dependency_type == DependencyType.LOCAL:
                    target_file = self._resolve_local_import(import_info.imported_module, source_file)
                    
                    if target_file and target_file in self.nodes:
                        self.graph.add_edge(source_file, target_file, **{
                            'import_type': import_info.import_type.value,
                            'imported_names': import_info.imported_names,
                            'line_number': import_info.line_number
                        })
    
    def _resolve_local_import(self, import_path: str, source_file: str) -> Optional[str]:
        """Resolve a local import path to an actual file path."""
        source_dir = Path(source_file).parent
        potential_paths = []

        if import_path.startswith('.'):
            # Relative import
            resolved_path = (source_dir / import_path).resolve()
            potential_paths.append(resolved_path)

            # Try with different extensions
            for ext in self.supported_extensions:
                potential_paths.append(resolved_path.with_suffix(ext))

            # Try index files
            if resolved_path.is_dir():
                for ext in self.supported_extensions:
                    potential_paths.append(resolved_path / f"index{ext}")
        else:
            # Project-relative import (like 'src/types/User')
            # Try resolving from project root
            project_root_path = self.project_root / import_path
            potential_paths.append(project_root_path)

            # Try with different extensions
            for ext in self.supported_extensions:
                potential_paths.append(project_root_path.with_suffix(ext))

            # Try index files
            if project_root_path.is_dir():
                for ext in self.supported_extensions:
                    potential_paths.append(project_root_path / f"index{ext}")

        # Find the first existing file
        for path in potential_paths:
            if path.exists() and str(path) in self.nodes:
                return str(path)

        return None
    
    def _analyze_graph_properties(self) -> None:
        """Analyze various properties of the dependency graph."""
        # Analyze each node to determine its role in the dependency graph
        for file_path, node in self.nodes.items():
            # Entry point detection: files that import others but aren't imported
            # These are typically main.ts, index.tsx, or app entry files
            in_degree = self.graph.in_degree(file_path)
            out_degree = self.graph.out_degree(file_path)

            # A file is an entry point if:
            # 1. It has no incoming dependencies (nothing imports it)
            # 2. It has outgoing dependencies (it imports other files)
            # 3. OR it was already marked as entry point during parsing (main.ts, etc.)
            if (in_degree == 0 and out_degree > 0) or node.is_entry_point:
                node.is_entry_point = True
                self.graph.nodes[file_path]['is_entry_point'] = True

            # Leaf node detection: files that don't import anything but are imported
            # These are typically utility files, components with no dependencies
            # A file is a leaf node if:
            # 1. It has no outgoing dependencies (imports nothing)
            # 2. It has incoming dependencies (other files import it)
            # 3. OR it was already marked as leaf during parsing
            if (out_degree == 0 and in_degree > 0) or node.is_leaf_node:
                node.is_leaf_node = True
                self.graph.nodes[file_path]['is_leaf_node'] = True

    def find_circular_dependencies(self) -> List[List[str]]:
        """Find all circular dependencies in the graph."""
        try:
            # Use NetworkX's simple_cycles algorithm to find all cycles
            # This algorithm finds all elementary cycles (cycles with no repeated nodes)
            # in the directed graph, which correspond to circular dependencies
            cycles = list(nx.simple_cycles(self.graph))

            # Filter out self-loops (a file importing itself) if they exist
            # Self-loops are technically circular dependencies but often less critical
            meaningful_cycles = [cycle for cycle in cycles if len(cycle) > 1]

            return meaningful_cycles
        except nx.NetworkXError:
            # Handle cases where the graph might be malformed or empty
            return []
    
    def get_entry_points(self) -> List[str]:
        """Get all entry points in the project."""
        return [
            file_path for file_path, node in self.nodes.items()
            if node.is_entry_point or self.graph.in_degree(file_path) == 0
        ]
    
    def get_leaf_nodes(self) -> List[str]:
        """Get all leaf nodes (files with no dependencies)."""
        return [
            file_path for file_path, node in self.nodes.items()
            if node.is_leaf_node or self.graph.out_degree(file_path) == 0
        ]
    
    def get_dependency_chain(self, file_path: str) -> List[str]:
        """Get the dependency chain for a specific file."""
        if file_path not in self.graph:
            return []
        
        # Use DFS to get all dependencies
        visited = set()
        chain = []
        
        def dfs(current_file):
            if current_file in visited:
                return
            
            visited.add(current_file)
            chain.append(current_file)
            
            for successor in self.graph.successors(current_file):
                dfs(successor)
        
        dfs(file_path)
        return chain
    
    def get_dependents(self, file_path: str) -> List[str]:
        """Get all files that depend on the given file."""
        if file_path not in self.graph:
            return []
        
        return list(self.graph.predecessors(file_path))
    
    def get_dependencies(self, file_path: str) -> List[str]:
        """Get all direct dependencies of the given file."""
        if file_path not in self.graph:
            return []
        
        return list(self.graph.successors(file_path))
    
    def get_external_dependencies(self) -> Dict[str, List[str]]:
        """Get all external dependencies grouped by package."""
        external_deps = defaultdict(list)
        
        for file_path, node in self.nodes.items():
            for import_info in node.imports:
                if import_info.dependency_type == DependencyType.EXTERNAL:
                    package_name = import_info.imported_module.split('/')[0]
                    external_deps[package_name].append(file_path)
        
        return dict(external_deps)
    
    def get_statistics(self) -> Dict:
        """Get comprehensive statistics about the dependency graph."""
        total_files = len(self.nodes)
        total_edges = self.graph.number_of_edges()
        
        # Calculate various metrics
        in_degrees = [self.graph.in_degree(node) for node in self.graph.nodes()]
        out_degrees = [self.graph.out_degree(node) for node in self.graph.nodes()]
        
        external_deps = self.get_external_dependencies()
        cycles = self.find_circular_dependencies()
        
        stats = {
            'total_files': total_files,
            'total_dependencies': total_edges,
            'entry_points': len(self.get_entry_points()),
            'leaf_nodes': len(self.get_leaf_nodes()),
            'circular_dependencies': len(cycles),
            'external_packages': len(external_deps),
            'avg_dependencies_per_file': sum(out_degrees) / total_files if total_files > 0 else 0,
            'avg_dependents_per_file': sum(in_degrees) / total_files if total_files > 0 else 0,
            'max_dependencies': max(out_degrees) if out_degrees else 0,
            'max_dependents': max(in_degrees) if in_degrees else 0,
            'file_types': self._get_file_type_distribution(),
            'most_imported_files': self._get_most_imported_files(5),
            'most_dependent_files': self._get_most_dependent_files(5)
        }
        
        return stats
    
    def _get_file_type_distribution(self) -> Dict[str, int]:
        """Get distribution of file types in the project."""
        distribution = defaultdict(int)
        
        for file_path in self.nodes:
            ext = Path(file_path).suffix
            distribution[ext] += 1
        
        return dict(distribution)
    
    def _get_most_imported_files(self, limit: int = 5) -> List[Tuple[str, int]]:
        """Get the most imported files (highest in-degree)."""
        files_with_counts = [
            (file_path, self.graph.in_degree(file_path))
            for file_path in self.graph.nodes()
        ]
        
        files_with_counts.sort(key=lambda x: x[1], reverse=True)
        return files_with_counts[:limit]
    
    def _get_most_dependent_files(self, limit: int = 5) -> List[Tuple[str, int]]:
        """Get files with the most dependencies (highest out-degree)."""
        files_with_counts = [
            (file_path, self.graph.out_degree(file_path))
            for file_path in self.graph.nodes()
        ]
        
        files_with_counts.sort(key=lambda x: x[1], reverse=True)
        return files_with_counts[:limit]
    
    def get_shortest_path(self, source: str, target: str) -> Optional[List[str]]:
        """Get the shortest dependency path between two files."""
        try:
            path = nx.shortest_path(self.graph, source, target)
            return list(path)  # Ensure we return a list of strings
        except (nx.NetworkXNoPath, nx.NodeNotFound):
            return None
    
    def is_reachable(self, source: str, target: str) -> bool:
        """Check if target file is reachable from source file."""
        try:
            nx.shortest_path(self.graph, source, target)
            return True
        except (nx.NetworkXNoPath, nx.NodeNotFound):
            return False

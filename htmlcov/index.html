<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_6b508a39.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">80%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button current">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.1">coverage.py v7.10.1</a>,
            created at 2025-08-01 21:25 +0800
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb___init___py.html">src/diff_analyzer/__init__.py</a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_analyzer_py.html">src/diff_analyzer/dependency_analyzer.py</a></td>
                <td>165</td>
                <td>14</td>
                <td>1</td>
                <td class="right" data-ratio="151 165">92%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_graph_py.html">src/diff_analyzer/dependency_graph.py</a></td>
                <td>166</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="132 166">80%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_main_py.html">src/diff_analyzer/main.py</a></td>
                <td>133</td>
                <td>102</td>
                <td>5</td>
                <td class="right" data-ratio="31 133">23%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_output_formatters_py.html">src/diff_analyzer/output_formatters.py</a></td>
                <td>179</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="162 179">91%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_react_parser_py.html">src/diff_analyzer/react_parser.py</a></td>
                <td>150</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="142 150">95%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_vue_parser_py.html">src/diff_analyzer/vue_parser.py</a></td>
                <td>139</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="124 139">89%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td>941</td>
                <td>190</td>
                <td>6</td>
                <td class="right" data-ratio="751 941">80%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.1">coverage.py v7.10.1</a>,
            created at 2025-08-01 21:25 +0800
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href="z_fb503d2f3ff24abb_vue_parser_py.html"></a>
        <a id="nextFileLink" class="nav" href="z_fb503d2f3ff24abb___init___py.html"></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>

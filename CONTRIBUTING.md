# Contributing to Vue.js and React Dependency Analyzer

We welcome contributions! This document provides guidelines for contributing to the project.

## Getting Started

### Prerequisites

- Python 3.11 or higher
- uv (recommended) or pip for package management
- Git for version control

### Development Setup

1. **Fork and clone the repository**
   ```bash
   git clone https://github.com/your-username/diff_analyzer.git
   cd diff_analyzer
   ```

2. **Set up the development environment**
   ```bash
   # Using uv (recommended)
   uv install --group test --group dev
   
   # Or using pip
   pip install -e ".[test,dev]"
   ```

3. **Verify the setup**
   ```bash
   uv run pytest
   ```

## Development Workflow

### Making Changes

1. **Create a feature branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make your changes**
   - Follow the existing code style and architecture patterns
   - Add comprehensive tests for new functionality
   - Update documentation for API changes

3. **Run tests and quality checks**
   ```bash
   # Run tests
   uv run pytest
   
   # Check code formatting
   uv run black src tests
   uv run isort src tests
   
   # Check linting
   uv run flake8 src tests
   
   # Type checking
   uv run mypy src
   ```

4. **Commit your changes**
   ```bash
   git add .
   git commit -m "feat: add your feature description"
   ```

### Commit Message Format

We follow the [Conventional Commits](https://www.conventionalcommits.org/) specification:

- `feat:` - New features
- `fix:` - Bug fixes
- `docs:` - Documentation changes
- `style:` - Code style changes (formatting, etc.)
- `refactor:` - Code refactoring
- `test:` - Adding or updating tests
- `chore:` - Maintenance tasks

### Pull Request Process

1. **Push your branch**
   ```bash
   git push origin feature/your-feature-name
   ```

2. **Create a pull request**
   - Provide a clear description of the changes
   - Reference any related issues
   - Ensure all tests pass
   - Request review from maintainers

3. **Address feedback**
   - Make requested changes
   - Update tests and documentation as needed
   - Push updates to your branch

## Code Guidelines

### Code Style

- Follow PEP 8 style guidelines
- Use type hints consistently
- Add docstrings to all public methods and classes
- Keep functions focused and well-named
- Use meaningful variable names

### Testing

- Write tests for all new functionality
- Maintain high test coverage (80%+ overall, 90%+ core modules)
- Use descriptive test names
- Follow the Arrange-Act-Assert pattern
- Mock external dependencies appropriately

### Documentation

- Update API documentation for new classes and methods
- Add examples for new features
- Update the README if needed
- Ensure all code examples work correctly

## Project Structure

```
diff_analyzer/
├── src/diff_analyzer/     # Main package source code
├── tests/                 # Test suite
├── examples/              # Example projects
├── docs/                  # Documentation
└── pyproject.toml         # Project configuration
```

## Extending the System

### Adding New Parsers

See [docs/DEVELOPER_GUIDE.md](docs/DEVELOPER_GUIDE.md) for detailed instructions on:
- Creating new file parsers
- Adding output formatters
- Customizing analysis behavior

### Testing Extensions

- Add comprehensive unit tests
- Include integration tests for complete workflows
- Test error conditions and edge cases
- Maintain coverage requirements

## Getting Help

- Check existing [issues](https://github.com/your-org/diff_analyzer/issues)
- Read the [documentation](docs/)
- Ask questions in discussions

## Code of Conduct

- Be respectful and inclusive
- Focus on constructive feedback
- Help others learn and grow
- Follow project guidelines

Thank you for contributing!

# Vue.js and React Dependency Analyzer

A comprehensive Python tool that uses tree-sitter-typescript to parse Vue.js and React project source code and extract dependency relationships. This tool provides detailed analysis of component hierarchies, import/export relationships, and dependency graphs with multiple output formats.

## Features

### 🔍 **Multi-Framework Support**
- **Vue.js**: Parse `.vue` Single File Components with `<script>`, `<template>`, and `<style>` sections
- **React**: Analyze `.jsx`, `.tsx`, `.ts`, and `.js` files with JSX component usage
- **TypeScript**: Full TypeScript support with type-only imports detection

### 📊 **Dependency Analysis**
- **Import Detection**: ES6 imports, CommonJS require, dynamic imports
- **Component Relationships**: Vue component registrations, React component usage
- **Path Resolution**: Relative imports, absolute imports, and external dependencies
- **Circular Dependencies**: Automatic detection and reporting

### 📈 **Visualization & Output**
- **JSON**: Structured data with detailed import/export information
- **DOT**: Graphviz format for professional dependency graphs
- **Mermaid**: Interactive diagrams for documentation
- **Text Tree**: Human-readable hierarchical structure

### 🛠 **Advanced Features**
- **Entry Point Detection**: Automatically identify application entry points
- **Leaf Node Analysis**: Find components with no dependencies
- **Statistics**: Comprehensive metrics about your project structure
- **Path Analysis**: Find dependency paths between any two files

## Installation

### Using uv (Recommended)

```bash
# Clone the repository
git clone <repository-url>
cd diff_analyzer

# Install the package and dependencies
uv install

# Install with development dependencies
uv install --group test --group dev
```

### Using pip

```bash
# Clone the repository
git clone <repository-url>
cd diff_analyzer

# Install in development mode
pip install -e .

# Install with test dependencies
pip install -e ".[test]"

# Install with all dependencies
pip install -e ".[test,dev]"
```

## Quick Start

### Basic Analysis

```bash
# Analyze a Vue.js project
uv run python -m src.diff_analyzer.main ./my-vue-project

# Or using the CLI script
./diff_analyzer ./my-vue-project

# Analyze a React project with verbose output
uv run python -m src.diff_analyzer.main ./my-react-app --verbose

# Generate JSON output
uv run python -m src.diff_analyzer.main ./project --format json --output dependencies.json
```

### Advanced Usage

```bash
# Generate Mermaid diagram
uv run python -m src.diff_analyzer.main ./project --format mermaid --output diagram.md

# Create Graphviz DOT file
uv run python -m src.diff_analyzer.main ./project --format dot --output graph.dot

# Show detailed information
uv run python -m src.diff_analyzer.main ./project --show-details

# Exclude specific directories
uv run python -m src.diff_analyzer.main ./project --exclude node_modules dist build

# Show only statistics
uv run python -m src.diff_analyzer.main ./project --stats-only
```

### Using with pip installation

```bash
# If installed with pip, you can use the package directly
python -m src.diff_analyzer.main ./project --format json
```

## Output Formats

### 1. Text Tree (Default)
```
Dependency Tree Structure
==================================================

Total Files: 13
Total Dependencies: 22
Entry Points: 4
Leaf Nodes: 3

Entry Points:
--------------------
🚀 src/main.ts
├──
│   📁 src/App.vue
│   ├──
│   │   📁 src/components/UserProfile.vue
│   └──
│       📁 src/store/index.ts
```

### 2. JSON Format
```json
{
  "project_root": "/path/to/project",
  "statistics": {
    "total_files": 13,
    "total_dependencies": 22,
    "entry_points": 4,
    "circular_dependencies": 0
  },
  "files": {
    "src/main.ts": {
      "dependencies": ["src/App.vue", "src/router/index.ts"],
      "imports": [
        {
          "module": "vue",
          "type": "named",
          "dependency_type": "external",
          "imported_names": ["createApp"]
        }
      ]
    }
  }
}
```

### 3. Mermaid Diagram
```mermaid
flowchart TD
  N0["src/main.ts"]
  N1["src/App.vue"]
  N2["src/components/UserProfile.vue"]

  N0 --> N1
  N1 --> N2
```

### 4. DOT (Graphviz)
```dot
digraph DependencyGraph {
  rankdir=TB;
  node [shape=box, style=filled];

  "src/main.ts" [fillcolor=lightgreen];
  "src/App.vue" -> "src/components/UserProfile.vue";
}
```

## Supported File Types

| Extension | Framework | Parser |
|-----------|-----------|---------|
| `.vue` | Vue.js | Vue SFC Parser |
| `.tsx` | React/TypeScript | TSX Parser |
| `.jsx` | React | JSX Parser |
| `.ts` | TypeScript | TypeScript Parser |
| `.js` | JavaScript | TypeScript Parser |

## Configuration Options

### Command Line Options

| Option | Description | Default |
|--------|-------------|---------|
| `--format` | Output format (text, json, dot, mermaid) | text |
| `--output` | Output file path | stdout |
| `--exclude` | Patterns to exclude | node_modules, .git, dist |
| `--show-details` | Include detailed import information | false |
| `--verbose` | Enable verbose logging | false |
| `--stats-only` | Show only statistics | false |

### Exclusion Patterns

By default, the tool excludes common build and dependency directories:
- `node_modules`
- `.git`
- `dist`
- `build`
- `.next` (Next.js)
- `.nuxt` (Nuxt.js)

## Examples

### Vue.js Project Analysis

```bash
# Basic Vue.js analysis
python main.py ./vue-project --format text

# Generate dependency graph for documentation
python main.py ./vue-project --format mermaid --output docs/dependencies.md

# Find circular dependencies
python main.py ./vue-project --find-cycles
```

### React Project Analysis

```bash
# Analyze React TypeScript project
python main.py ./react-ts-project --show-details

# Export to JSON for further processing
python main.py ./react-project --format json --output analysis.json

# Check component relationships
python main.py analyze-file ./react-project src/components/App.tsx
```

## Project Structure

```
diff_analyzer/
├── src/
│   └── diff_analyzer/
│       ├── __init__.py         # Package initialization and exports
│       ├── main.py             # CLI interface and main application
│       ├── dependency_analyzer.py  # Core dependency extraction classes
│       ├── vue_parser.py       # Vue.js specific parser
│       ├── react_parser.py     # React/TypeScript parser
│       ├── dependency_graph.py # Graph analysis and algorithms
│       └── output_formatters.py # Multiple output format support
├── tests/                      # Comprehensive test suite
│   ├── conftest.py            # Test fixtures and configuration
│   ├── test_dependency_analyzer.py
│   ├── test_vue_parser.py
│   ├── test_react_parser.py
│   ├── test_dependency_graph.py
│   ├── test_output_formatters.py
│   └── test_integration.py
├── examples/                   # Example projects for testing
│   ├── vue_project/           # Example Vue.js project
│   └── react_project/         # Example React project
├── docs/                      # Documentation
│   ├── API.md                 # API reference
│   ├── DEVELOPER_GUIDE.md     # Developer and extension guide
│   └── TESTING_GUIDE.md       # Testing documentation
├── pyproject.toml             # Project configuration and dependencies
├── diff_analyzer              # CLI entry point script
└── README.md
```

## API Usage

You can also use the tool programmatically:

```python
from src.diff_analyzer import DependencyGraph, JSONFormatter

# Analyze a project
graph = DependencyGraph("/path/to/project")
graph.analyze_project()

# Get statistics
stats = graph.get_statistics()
print(f"Total files: {stats['total_files']}")

# Find circular dependencies
cycles = graph.find_circular_dependencies()
print(f"Found {len(cycles)} circular dependencies")

# Generate output
formatter = JSONFormatter(graph)
json_output = formatter.format()
```

## Testing

The project includes comprehensive unit tests and integration tests to ensure reliability and maintainability.

### Running Tests

```bash
# Using uv (recommended)
uv install --group test
uv run pytest

# Using pip
pip install -e ".[test]"
pytest

# Run with coverage
uv run pytest --cov=src --cov-report=html

# Run specific test categories
uv run pytest -m unit          # Unit tests only
uv run pytest -m integration   # Integration tests only
uv run pytest -m "not slow"    # Exclude slow tests
```

### Test Structure

- **Unit Tests**: Test individual components in isolation
- **Integration Tests**: Test complete workflows with real projects
- **Parser Tests**: Test file parsing capabilities for Vue and React
- **Formatter Tests**: Test output generation in various formats

### Coverage

The test suite maintains high coverage across all modules:
- Overall coverage: 80%+
- Core modules: 90%+
- Parsers: 85%+
- Formatters: 80%+

For detailed testing information, see [docs/TESTING_GUIDE.md](docs/TESTING_GUIDE.md).

## API Documentation

### Programmatic Usage

```python
from src.diff_analyzer import (
    DependencyGraph,
    JSONFormatter,
    MermaidFormatter
)

# Analyze a project
graph = DependencyGraph("/path/to/project")
graph.analyze_project()

# Get comprehensive statistics
stats = graph.get_statistics()
print(f"Total files: {stats['total_files']}")
print(f"Circular dependencies: {stats['circular_dependencies']}")

# Find specific relationships
dependencies = graph.get_dependencies("/path/to/file.vue")
dependents = graph.get_dependents("/path/to/file.vue")

# Generate different output formats
json_formatter = JSONFormatter(graph)
json_output = json_formatter.format(include_details=True)

mermaid_formatter = MermaidFormatter(graph)
mermaid_diagram = mermaid_formatter.format(diagram_type='flowchart')
```

### Extension Points

The analyzer is designed to be extensible:

- **Custom Parsers**: Add support for new frameworks or file types
- **Custom Formatters**: Create new output formats for specific tools
- **Custom Analysis**: Extend graph analysis with domain-specific logic

For detailed API documentation, see [docs/API.md](docs/API.md).

For developer guidance and extension examples, see [docs/DEVELOPER_GUIDE.md](docs/DEVELOPER_GUIDE.md).

## Contributing

We welcome contributions! Please follow these guidelines:

1. **Fork the repository** and create a feature branch
2. **Add comprehensive tests** for new functionality
3. **Follow the existing code style** and architecture patterns
4. **Update documentation** for API changes
5. **Ensure all tests pass** with good coverage
6. **Submit a pull request** with a clear description

### Development Setup

```bash
# Clone the repository
git clone <repository-url>
cd diff_analyzer

# Install in development mode with all dependencies (using uv)
uv install --group test --group dev

# Or using pip
pip install -e ".[test,dev]"

# Install pre-commit hooks (if available)
pre-commit install

# Run tests to verify setup
uv run pytest
```

### Code Quality

The project uses several tools to maintain code quality:

- **Black**: Code formatting
- **isort**: Import sorting
- **flake8**: Linting
- **mypy**: Type checking
- **pytest**: Testing with coverage

Run quality checks:

```bash
# Using uv
uv run black src tests
uv run isort src tests
uv run flake8 src tests
uv run mypy src
uv run pytest --cov=src

# Or using pip installation
black src tests
isort src tests
flake8 src tests
mypy src
pytest --cov=src
```

## License

MIT License - see LICENSE file for details.

## Troubleshooting

### Common Issues

1. **"No files found"**: Check that your project contains supported file types (.vue, .tsx, .jsx, .ts, .js)
2. **"Dependencies not resolved"**: Ensure import paths are correct and files exist
3. **"Tree-sitter errors"**: Make sure tree-sitter-typescript is properly installed

### Debug Mode

Use `--verbose` flag to see detailed parsing information:

```bash
uv run python -m src.diff_analyzer.main ./project --verbose
```

## Roadmap

- [ ] Support for more frameworks (Angular, Svelte)
- [ ] Integration with popular bundlers (Webpack, Vite)
- [ ] Performance optimizations for large projects
- [ ] Web-based visualization interface
- [ ] CI/CD integration tools
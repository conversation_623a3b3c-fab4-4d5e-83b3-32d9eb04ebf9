<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_6b508a39.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">80%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button current">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.1">coverage.py v7.10.1</a>,
            created at 2025-08-01 21:25 +0800
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">class<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb___init___py.html">src/diff_analyzer/__init__.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_analyzer_py.html#t21">src/diff_analyzer/dependency_analyzer.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_analyzer_py.html#t21"><data value='ImportType'>ImportType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_analyzer_py.html#t39">src/diff_analyzer/dependency_analyzer.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_analyzer_py.html#t39"><data value='DependencyType'>DependencyType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_analyzer_py.html#t54">src/diff_analyzer/dependency_analyzer.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_analyzer_py.html#t54"><data value='ImportInfo'>ImportInfo</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_analyzer_py.html#t92">src/diff_analyzer/dependency_analyzer.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_analyzer_py.html#t92"><data value='DependencyNode'>DependencyNode</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_analyzer_py.html#t123">src/diff_analyzer/dependency_analyzer.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_analyzer_py.html#t123"><data value='BaseParser'>BaseParser</data></a></td>
                <td>116</td>
                <td>14</td>
                <td>1</td>
                <td class="right" data-ratio="102 116">88%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_analyzer_py.html">src/diff_analyzer/dependency_analyzer.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_analyzer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>49</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="49 49">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_graph_py.html#t28">src/diff_analyzer/dependency_graph.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_graph_py.html#t28"><data value='DependencyGraph'>DependencyGraph</data></a></td>
                <td>136</td>
                <td>34</td>
                <td>0</td>
                <td class="right" data-ratio="102 136">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_graph_py.html">src/diff_analyzer/dependency_graph.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_dependency_graph_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>30</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="30 30">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_main_py.html">src/diff_analyzer/main.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_main_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>133</td>
                <td>102</td>
                <td>5</td>
                <td class="right" data-ratio="31 133">23%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_output_formatters_py.html#t29">src/diff_analyzer/output_formatters.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_output_formatters_py.html#t29"><data value='OutputFormatter'>OutputFormatter</data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_output_formatters_py.html#t103">src/diff_analyzer/output_formatters.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_output_formatters_py.html#t103"><data value='JSONFormatter'>JSONFormatter</data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_output_formatters_py.html#t188">src/diff_analyzer/output_formatters.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_output_formatters_py.html#t188"><data value='DOTFormatter'>DOTFormatter</data></a></td>
                <td>43</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="36 43">84%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_output_formatters_py.html#t262">src/diff_analyzer/output_formatters.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_output_formatters_py.html#t262"><data value='MermaidFormatter'>MermaidFormatter</data></a></td>
                <td>38</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="38 38">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_output_formatters_py.html#t339">src/diff_analyzer/output_formatters.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_output_formatters_py.html#t339"><data value='TextTreeFormatter'>TextTreeFormatter</data></a></td>
                <td>58</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="49 58">84%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_output_formatters_py.html">src/diff_analyzer/output_formatters.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_output_formatters_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>23</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="22 23">96%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_react_parser_py.html#t25">src/diff_analyzer/react_parser.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_react_parser_py.html#t25"><data value='ReactParser'>ReactParser</data></a></td>
                <td>127</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="119 127">94%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_react_parser_py.html">src/diff_analyzer/react_parser.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_react_parser_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>23</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="23 23">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_vue_parser_py.html#t24">src/diff_analyzer/vue_parser.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_vue_parser_py.html#t24"><data value='VueParser'>VueParser</data></a></td>
                <td>119</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="104 119">87%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_fb503d2f3ff24abb_vue_parser_py.html">src/diff_analyzer/vue_parser.py</a></td>
                <td class="name left"><a href="z_fb503d2f3ff24abb_vue_parser_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>20</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="20 20">100%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>941</td>
                <td>190</td>
                <td>6</td>
                <td class="right" data-ratio="751 941">80%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.1">coverage.py v7.10.1</a>,
            created at 2025-08-01 21:25 +0800
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>

["tests/test_dependency_analyzer.py::TestBaseParser::test_base_parser_initialization", "tests/test_dependency_analyzer.py::TestBaseParser::test_clean_import_path", "tests/test_dependency_analyzer.py::TestBaseParser::test_extract_exports_from_tree", "tests/test_dependency_analyzer.py::TestBaseParser::test_extract_imports_from_tree", "tests/test_dependency_analyzer.py::TestBaseParser::test_find_project_root", "tests/test_dependency_analyzer.py::TestBaseParser::test_find_project_root_no_indicators", "tests/test_dependency_analyzer.py::TestBaseParser::test_get_parser_for_file_extension", "tests/test_dependency_analyzer.py::TestBaseParser::test_is_entry_point", "tests/test_dependency_analyzer.py::TestBaseParser::test_node_to_attributes", "tests/test_dependency_analyzer.py::TestBaseParser::test_parse_file_not_implemented", "tests/test_dependency_analyzer.py::TestBaseParser::test_process_import_statement_default", "tests/test_dependency_analyzer.py::TestBaseParser::test_process_import_statement_named", "tests/test_dependency_analyzer.py::TestBaseParser::test_process_require_statement", "tests/test_dependency_analyzer.py::TestBaseParser::test_read_file_content", "tests/test_dependency_analyzer.py::TestBaseParser::test_read_file_content_file_not_found", "tests/test_dependency_analyzer.py::TestBaseParser::test_read_file_content_unicode_error", "tests/test_dependency_analyzer.py::TestBaseParser::test_resolve_import_path_absolute", "tests/test_dependency_analyzer.py::TestBaseParser::test_resolve_import_path_builtin", "tests/test_dependency_analyzer.py::TestBaseParser::test_resolve_import_path_external", "tests/test_dependency_analyzer.py::TestBaseParser::test_resolve_import_path_relative", "tests/test_dependency_analyzer.py::TestBaseParserIntegration::test_parse_jsx_file", "tests/test_dependency_analyzer.py::TestBaseParserIntegration::test_parse_typescript_file", "tests/test_dependency_analyzer.py::TestDependencyNode::test_dependency_node_creation", "tests/test_dependency_analyzer.py::TestDependencyNode::test_dependency_node_defaults", "tests/test_dependency_analyzer.py::TestDependencyNode::test_dependency_node_entry_point", "tests/test_dependency_analyzer.py::TestDependencyNode::test_dependency_node_leaf_node", "tests/test_dependency_analyzer.py::TestDependencyType::test_dependency_type_values", "tests/test_dependency_analyzer.py::TestImportInfo::test_import_info_creation", "tests/test_dependency_analyzer.py::TestImportInfo::test_import_info_defaults", "tests/test_dependency_analyzer.py::TestImportInfo::test_import_info_type_only", "tests/test_dependency_analyzer.py::TestImportType::test_import_type_values", "tests/test_dependency_graph.py::TestDependencyGraph::test_analyze_graph_properties", "tests/test_dependency_graph.py::TestDependencyGraph::test_analyze_project_integration", "tests/test_dependency_graph.py::TestDependencyGraph::test_build_graph_edges", "tests/test_dependency_graph.py::TestDependencyGraph::test_dependency_graph_initialization", "tests/test_dependency_graph.py::TestDependencyGraph::test_find_circular_dependencies_no_cycles", "tests/test_dependency_graph.py::TestDependencyGraph::test_find_circular_dependencies_self_loop", "tests/test_dependency_graph.py::TestDependencyGraph::test_find_circular_dependencies_with_cycles", "tests/test_dependency_graph.py::TestDependencyGraph::test_find_dependency_path_exists", "tests/test_dependency_graph.py::TestDependencyGraph::test_find_dependency_path_not_exists", "tests/test_dependency_graph.py::TestDependencyGraph::test_find_dependency_path_same_node", "tests/test_dependency_graph.py::TestDependencyGraph::test_find_files_to_analyze", "tests/test_dependency_graph.py::TestDependencyGraph::test_find_files_to_analyze_empty_directory", "tests/test_dependency_graph.py::TestDependencyGraph::test_get_dependencies", "tests/test_dependency_graph.py::TestDependencyGraph::test_get_dependencies_nonexistent", "tests/test_dependency_graph.py::TestDependencyGraph::test_get_dependents", "tests/test_dependency_graph.py::TestDependencyGraph::test_get_dependents_nonexistent", "tests/test_dependency_graph.py::TestDependencyGraph::test_get_entry_points", "tests/test_dependency_graph.py::TestDependencyGraph::test_get_external_dependencies", "tests/test_dependency_graph.py::TestDependencyGraph::test_get_leaf_nodes", "tests/test_dependency_graph.py::TestDependencyGraph::test_get_statistics", "tests/test_dependency_graph.py::TestDependencyGraph::test_node_to_attributes", "tests/test_dependency_graph.py::TestDependencyGraph::test_parse_file_react", "tests/test_dependency_graph.py::TestDependencyGraph::test_parse_file_unsupported", "tests/test_dependency_graph.py::TestDependencyGraph::test_parse_file_vue", "tests/test_integration.py::TestCLIIntegration::test_cli_basic_analysis", "tests/test_integration.py::TestCLIIntegration::test_cli_json_output_to_file", "tests/test_integration.py::TestCLIIntegration::test_cli_mermaid_output_to_file", "tests/test_integration.py::TestErrorHandlingIntegration::test_circular_dependency_detection", "tests/test_integration.py::TestErrorHandlingIntegration::test_empty_project_directory", "tests/test_integration.py::TestErrorHandlingIntegration::test_nonexistent_project_directory", "tests/test_integration.py::TestErrorHandlingIntegration::test_project_with_invalid_files", "tests/test_integration.py::TestMixedProjectIntegration::test_mixed_project_analysis", "tests/test_integration.py::TestReactProjectIntegration::test_react_project_all_formatters", "tests/test_integration.py::TestReactProjectIntegration::test_react_project_analysis", "tests/test_integration.py::TestVueProjectIntegration::test_vue_project_analysis", "tests/test_integration.py::TestVueProjectIntegration::test_vue_project_dot_output", "tests/test_integration.py::TestVueProjectIntegration::test_vue_project_json_output", "tests/test_integration.py::TestVueProjectIntegration::test_vue_project_mermaid_output", "tests/test_integration.py::TestVueProjectIntegration::test_vue_project_text_output", "tests/test_output_formatters.py::TestDOTFormatter::test_dot_formatter_initialization", "tests/test_output_formatters.py::TestDOTFormatter::test_format_basic", "tests/test_output_formatters.py::TestDOTFormatter::test_format_with_clustering", "tests/test_output_formatters.py::TestDOTFormatter::test_format_with_external_dependencies", "tests/test_output_formatters.py::TestJSONFormatter::test_format_basic", "tests/test_output_formatters.py::TestJSONFormatter::test_format_external_import_info", "tests/test_output_formatters.py::TestJSONFormatter::test_format_import_info", "tests/test_output_formatters.py::TestJSONFormatter::test_format_with_circular_dependencies", "tests/test_output_formatters.py::TestJSONFormatter::test_format_with_details", "tests/test_output_formatters.py::TestJSONFormatter::test_json_formatter_initialization", "tests/test_output_formatters.py::TestMermaidFormatter::test_format_flowchart", "tests/test_output_formatters.py::TestMermaidFormatter::test_format_flowchart_node_styling", "tests/test_output_formatters.py::TestMermaidFormatter::test_format_graph", "tests/test_output_formatters.py::TestMermaidFormatter::test_format_unsupported_type", "tests/test_output_formatters.py::TestMermaidFormatter::test_mermaid_formatter_initialization", "tests/test_output_formatters.py::TestOutputFormatter::test_get_file_label", "tests/test_output_formatters.py::TestOutputFormatter::test_get_relative_path", "tests/test_output_formatters.py::TestOutputFormatter::test_get_relative_path_outside_project", "tests/test_output_formatters.py::TestOutputFormatter::test_output_formatter_initialization", "tests/test_output_formatters.py::TestOutputFormattersIntegration::test_all_formatters_produce_output", "tests/test_output_formatters.py::TestOutputFormattersIntegration::test_formatters_handle_empty_graph", "tests/test_output_formatters.py::TestTextTreeFormatter::test_format_basic", "tests/test_output_formatters.py::TestTextTreeFormatter::test_format_dependency_tree", "tests/test_output_formatters.py::TestTextTreeFormatter::test_format_dependency_tree_circular", "tests/test_output_formatters.py::TestTextTreeFormatter::test_format_with_circular_dependencies", "tests/test_output_formatters.py::TestTextTreeFormatter::test_format_with_details", "tests/test_output_formatters.py::TestTextTreeFormatter::test_text_tree_formatter_initialization", "tests/test_react_parser.py::TestReactParser::test_extract_jsx_component_usage", "tests/test_react_parser.py::TestReactParser::test_extract_prop_types", "tests/test_react_parser.py::TestReactParser::test_extract_react_components", "tests/test_react_parser.py::TestReactParser::test_extract_react_specific_imports", "tests/test_react_parser.py::TestReactParser::test_get_component_type", "tests/test_react_parser.py::TestReactParser::test_get_hooks_used", "tests/test_react_parser.py::TestReactParser::test_get_parser_for_file_js", "tests/test_react_parser.py::TestReactParser::test_get_parser_for_file_jsx", "tests/test_react_parser.py::TestReactParser::test_get_parser_for_file_ts", "tests/test_react_parser.py::TestReactParser::test_get_parser_for_file_tsx", "tests/test_react_parser.py::TestReactParser::test_get_react_specific_info", "tests/test_react_parser.py::TestReactParser::test_has_jsx", "tests/test_react_parser.py::TestReactParser::test_has_state_usage", "tests/test_react_parser.py::TestReactParser::test_parse_file_error_handling", "tests/test_react_parser.py::TestReactParser::test_parse_file_success", "tests/test_react_parser.py::TestReactParser::test_react_parser_initialization", "tests/test_react_parser.py::TestReactParser::test_uses_react_hooks", "tests/test_react_parser.py::TestReactParserIntegration::test_parse_complete_react_component", "tests/test_react_parser.py::TestReactParserIntegration::test_parse_react_class_component", "tests/test_react_parser.py::TestReactParserIntegration::test_parse_react_hooks_only_file", "tests/test_vue_parser.py::TestVueParser::test_extract_component_name", "tests/test_vue_parser.py::TestVueParser::test_extract_component_name_define_component", "tests/test_vue_parser.py::TestVueParser::test_extract_component_name_not_found", "tests/test_vue_parser.py::TestVueParser::test_extract_computed_properties", "tests/test_vue_parser.py::TestVueParser::test_extract_methods", "tests/test_vue_parser.py::TestVueParser::test_extract_props", "tests/test_vue_parser.py::TestVueParser::test_extract_script_sections_javascript", "tests/test_vue_parser.py::TestVueParser::test_extract_script_sections_multiple", "tests/test_vue_parser.py::TestVueParser::test_extract_script_sections_no_lang", "tests/test_vue_parser.py::TestVueParser::test_extract_script_sections_setup", "tests/test_vue_parser.py::TestVueParser::test_extract_script_sections_typescript", "tests/test_vue_parser.py::TestVueParser::test_extract_template_component_usage", "tests/test_vue_parser.py::TestVueParser::test_extract_vue_component_imports", "tests/test_vue_parser.py::TestVueParser::test_get_parser_for_script_default", "tests/test_vue_parser.py::TestVueParser::test_get_parser_for_script_jsx", "tests/test_vue_parser.py::TestVueParser::test_get_parser_for_script_typescript", "tests/test_vue_parser.py::TestVueParser::test_get_vue_specific_info", "tests/test_vue_parser.py::TestVueParser::test_parse_file_error_handling", "tests/test_vue_parser.py::TestVueParser::test_parse_file_success", "tests/test_vue_parser.py::TestVueParser::test_vue_parser_initialization", "tests/test_vue_parser.py::TestVueParserIntegration::test_parse_complete_vue_file", "tests/test_vue_parser.py::TestVueParserIntegration::test_parse_vue_file_composition_api", "tests/test_vue_parser.py::TestVueParserIntegration::test_parse_vue_file_with_script_setup"]
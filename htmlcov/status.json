{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.10.1", "globals": "6e898d898607ed07242520bd1fc531ff", "files": {"z_fb503d2f3ff24abb___init___py": {"hash": "c9a56944df022de2369e8d8d6454f64e", "index": {"url": "z_fb503d2f3ff24abb___init___py.html", "file": "src/diff_analyzer/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 9, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_fb503d2f3ff24abb_dependency_analyzer_py": {"hash": "827e1302f394071ec9239100de876648", "index": {"url": "z_fb503d2f3ff24abb_dependency_analyzer_py.html", "file": "src/diff_analyzer/dependency_analyzer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 165, "n_excluded": 1, "n_missing": 14, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_fb503d2f3ff24abb_dependency_graph_py": {"hash": "d89f282e6a59e1740d4977bd2ed72ccd", "index": {"url": "z_fb503d2f3ff24abb_dependency_graph_py.html", "file": "src/diff_analyzer/dependency_graph.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 166, "n_excluded": 0, "n_missing": 34, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_fb503d2f3ff24abb_main_py": {"hash": "ca4f40f356bdda10ca65a43fcece098d", "index": {"url": "z_fb503d2f3ff24abb_main_py.html", "file": "src/diff_analyzer/main.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 133, "n_excluded": 5, "n_missing": 102, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_fb503d2f3ff24abb_output_formatters_py": {"hash": "1d14c53b434f4ac03de20a4cb4feb612", "index": {"url": "z_fb503d2f3ff24abb_output_formatters_py.html", "file": "src/diff_analyzer/output_formatters.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 179, "n_excluded": 0, "n_missing": 17, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_fb503d2f3ff24abb_react_parser_py": {"hash": "867d8f2d99fe334c339931a5e5263a95", "index": {"url": "z_fb503d2f3ff24abb_react_parser_py.html", "file": "src/diff_analyzer/react_parser.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 150, "n_excluded": 0, "n_missing": 8, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_fb503d2f3ff24abb_vue_parser_py": {"hash": "db54e0953d16a2decfafcbc16a3b02c2", "index": {"url": "z_fb503d2f3ff24abb_vue_parser_py.html", "file": "src/diff_analyzer/vue_parser.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 139, "n_excluded": 0, "n_missing": 15, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}